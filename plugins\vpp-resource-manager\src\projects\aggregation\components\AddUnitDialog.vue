<template>
  <CetDialog
    v-bind="CetDialog_addUnit"
    v-on="CetDialog_addUnit.event"
    class="add-unit-dialog"
  >
    <div class="dialog-content">
      <!-- 基本信息表单 -->
      <div class="form-section">
        <CetForm
          :data.sync="CetForm_addUnit.data"
          v-bind="CetForm_addUnit"
          v-on="CetForm_addUnit.event"
        >
          <el-row :gutter="24">
            <!-- 机组名称 -->
            <el-col :span="8">
              <el-form-item label="机组名称" prop="unitName">
                <ElInput
                  v-model="CetForm_addUnit.data.unitName"
                  placeholder="请输入内容"
                />
              </el-form-item>
            </el-col>

            <!-- 机组类型 -->
            <el-col :span="8">
              <el-form-item label="机组类型" prop="unitType">
                <ElSelect
                  v-model="CetForm_addUnit.data.unitType"
                  placeholder="请选择"
                  @change="handleUnitTypeChange"
                >
                  <ElOption
                    v-for="item in unitTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </el-form-item>
            </el-col>
          </el-row>
        </CetForm>
      </div>

      <!-- 资源选择区域 - 只有选择了机组类型才显示 -->
      <div v-if="CetForm_addUnit.data.unitType" class="resource-section">
        <div class="resource-title">调峰机组资源列表</div>

        <!-- 搜索和筛选 -->
        <div class="resource-filter">
          <div class="filter-row">
            <!-- 搜索框 -->
            <div class="search-box">
              <ElInput
                v-model="resourceSearch"
                placeholder="请输入关键字"
                prefix-icon="el-icon-search"
                @input="handleResourceSearch"
              />
            </div>

            <!-- 区域筛选 -->
            <div class="area-select">
              <CustomElSelect
                v-model="selectedArea"
                :prefix_in="'区域'"
                @change="handleAreaChange"
              >
                <ElOption
                  v-for="item in areaOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </CustomElSelect>
            </div>

            <!-- 绑定资源按钮（暂时隐藏） -->
            <CetButton
              title="绑定资源"
              type="primary"
              class="bind-button"
              style="opacity: 0; pointer-events: none"
            />
          </div>
        </div>

        <!-- 资源表格 -->
        <div class="resource-table">
          <CetTable
            :data.sync="CetTable_resources.data"
            v-bind="CetTable_resources"
            v-on="CetTable_resources.event"
            @selection-change="handleResourceSelection"
            height="300px"
          >
            <!-- 多选列 -->
            <ElTableColumn
              type="selection"
              width="50"
              align="center"
              header-align="center"
            />

            <!-- 序号列 -->
            <ElTableColumn
              type="index"
              label="序号"
              width="72"
              align="center"
              header-align="center"
            />

            <!-- 资源ID列 -->
            <ElTableColumn
              prop="resourceId"
              label="资源ID"
              align="center"
              header-align="center"
              show-overflow-tooltip
            />

            <!-- 资源名称列 -->
            <ElTableColumn
              prop="resourceName"
              label="资源名称"
              align="left"
              header-align="left"
              show-overflow-tooltip
            />

            <!-- 区域列 -->
            <ElTableColumn
              prop="area"
              label="区域"
              align="center"
              header-align="center"
              show-overflow-tooltip
            />

            <!-- 容量列 -->
            <ElTableColumn
              prop="capacity"
              label="容量(MW)"
              align="center"
              header-align="center"
              show-overflow-tooltip
            />
          </CetTable>

          <!-- 分页器 -->
          <div class="pagination-wrapper">
            <div class="pagination-total">
              共
              <span class="total-number">{{ total }}</span>
              个
            </div>
            <el-pagination
              class="pagination-container"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 15, 20, 50, 100]"
              :page-size="pageSize"
              :total="total"
              layout="sizes, prev, pager, next, jumper"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
        class="cancel-button"
      />
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
        class="confirm-button"
      />
    </template>
  </CetDialog>
</template>

<script>
import { CustomElSelect } from "@/base/components";

export default {
  name: "AddUnitDialog",
  components: {
    CustomElSelect
  },
  props: {
    visibleTrigger_in: {
      type: Number,
      default: 0
    },
    closeTrigger_in: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 弹窗配置
      CetDialog_addUnit: {
        title: "新增",
        width: "960px",
        openTrigger_in: 0,
        closeTrigger_in: 0,
        showClose: true,
        "append-to-body": true,
        event: {
          openTrigger_out: this.handleDialogOpen,
          closeTrigger_out: this.handleDialogClose
        }
      },

      // 表单组件配置
      CetForm_addUnit: {
        dataMode: "component",
        queryMode: "trigger",
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          groups: []
        },
        inputData_in: {},
        data: {
          unitName: "",
          unitType: "peaking"
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          unitName: [
            { required: true, message: "请输入机组名称", trigger: "blur" }
          ],
          unitType: [
            { required: true, message: "请选择机组类型", trigger: "change" }
          ]
        },
        event: {
          saveData_out: this.CetForm_addUnit_saveData_out
        }
      },

      // 机组类型选项
      unitTypeOptions: [
        { value: "peaking", label: "调峰机组" },
        { value: "thermal", label: "火电机组" },
        { value: "hydro", label: "水电机组" },
        { value: "wind", label: "风电机组" },
        { value: "solar", label: "光伏机组" }
      ],

      // 资源搜索和筛选
      resourceSearch: "",
      selectedArea: "",
      areaOptions: [
        { value: "", label: "全部" },
        { value: "area1", label: "区域1" },
        { value: "area2", label: "区域2" },
        { value: "area3", label: "区域3" }
      ],

      // 资源表格配置
      CetTable_resources: {
        data: [],
        queryMode: "component",
        showPagination: false,
        event: {}
      },

      // 分页数据
      allResourcesData: [],
      filteredResourcesData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 选中的资源
      selectedResources: [],

      // 按钮配置
      CetButton_cancel: {
        title: "取消",
        type: "default",
        event: {
          statusTrigger_out: this.handleCancel
        }
      },

      CetButton_confirm: {
        title: "确定",
        type: "primary",
        event: {
          statusTrigger_out: this.handleConfirm
        }
      }
    };
  },

  watch: {
    visibleTrigger_in(val) {
      if (val) {
        this.CetDialog_addUnit.openTrigger_in = val;
        this.initDialog();
      }
    },

    closeTrigger_in(val) {
      if (val) {
        this.CetDialog_addUnit.closeTrigger_in = val;
      }
    }
  },

  methods: {
    // 初始化弹窗
    initDialog() {
      this.resetForm();
      // 不在这里初始化资源数据，等待用户选择机组类型后再初始化
    },

    // 重置表单
    resetForm() {
      this.CetForm_addUnit.data = {
        unitName: "",
        unitType: ""
      };
      this.CetForm_addUnit.resetTrigger_in = new Date().getTime();
      this.resourceSearch = "";
      this.selectedArea = "";
      this.selectedResources = [];
      // 清空资源数据
      this.allResourcesData = [];
      this.filteredResourcesData = [];
      this.CetTable_resources.data = [];
      this.total = 0;
      this.currentPage = 1;
    },

    // 初始化资源数据
    initResourceData() {
      // 生成模拟资源数据
      this.allResourcesData = this.generateMockResourceData();
      this.filteredResourcesData = [...this.allResourcesData];
      this.total = this.filteredResourcesData.length;
      this.currentPage = 1;
      this.updateResourceTableData();
    },

    // 生成模拟资源数据
    generateMockResourceData() {
      const areas = ["区域1", "区域2", "区域3"];
      const data = [];

      for (let i = 1; i <= 50; i++) {
        const area = areas[Math.floor(Math.random() * areas.length)];
        data.push({
          id: i,
          resourceId: `RES${String(i).padStart(4, "0")}`,
          resourceName: `调峰资源${i}`,
          area: area,
          capacity: (Math.random() * 100 + 10).toFixed(1)
        });
      }

      return data;
    },

    // 更新资源表格数据
    updateResourceTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.CetTable_resources.data = this.filteredResourcesData.slice(
        start,
        end
      );
    },

    // 过滤资源数据
    filterResourceData() {
      const searchKeyword = this.resourceSearch.toLowerCase();
      const selectedArea = this.selectedArea;

      this.filteredResourcesData = this.allResourcesData.filter(item => {
        // 名称和ID过滤
        const nameMatch =
          !searchKeyword ||
          item.resourceName.toLowerCase().includes(searchKeyword) ||
          item.resourceId.toLowerCase().includes(searchKeyword);

        // 区域过滤
        const areaMatch = !selectedArea || item.area === selectedArea;

        return nameMatch && areaMatch;
      });

      // 重置分页
      this.currentPage = 1;
      this.total = this.filteredResourcesData.length;
      this.updateResourceTableData();
    },

    // 资源搜索处理
    handleResourceSearch() {
      this.filterResourceData();
    },

    // 机组类型变化处理
    handleUnitTypeChange(value) {
      console.log("机组类型变化:", value);
      // 当机组类型变化时，重新初始化资源数据
      if (value) {
        this.initResourceData();
      }
    },

    // 区域变化处理
    handleAreaChange() {
      this.filterResourceData();
    },

    // 分页大小变化
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1;
      this.updateResourceTableData();
    },

    // 当前页变化
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.updateResourceTableData();
    },

    // 资源选择变化
    handleResourceSelection(selection) {
      this.selectedResources = selection;
    },

    // 弹窗打开处理
    handleDialogOpen() {
      console.log("弹窗已打开");
    },

    // 弹窗关闭处理
    handleDialogClose() {
      this.$emit("closeTrigger_out", Date.now());
    },

    // 取消按钮处理
    handleCancel() {
      this.CetDialog_addUnit.closeTrigger_in = Date.now();
    },

    // 确定按钮处理
    handleConfirm() {
      // 触发表单保存验证
      this.CetForm_addUnit.localSaveTrigger_in = new Date().getTime();
    },

    // CetForm保存处理
    CetForm_addUnit_saveData_out(formData) {
      // 表单验证通过，构造提交数据
      const submitData = {
        unitName: formData.unitName.trim(),
        unitType: formData.unitType,
        selectedResources: this.selectedResources,
        resourceCount: this.selectedResources.length
      };

      // 触发保存事件
      this.$emit("confirm_out", submitData);

      // 关闭弹窗
      this.CetDialog_addUnit.closeTrigger_in = Date.now();

      this.$message.success("新增机组成功");
    }
  }
};
</script>

<style scoped>
.add-unit-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.add-unit-dialog :deep(.el-dialog__body) {
  background-color: var(--BG);
  padding: 8px;
}

.dialog-content {
  background-color: var(--BG1);
  border-radius: 8px;
  padding: 24px;
}

/* 表单区域 */
.form-section {
  margin-bottom: 24px;
}

.form-section :deep(.el-form-item__label) {
  font-size: 14px;
  color: var(--Text1);
  line-height: 22px;
}

.form-input,
.form-select {
  width: 100%;
}

/* 资源选择区域 */
.resource-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.resource-title {
  font-size: 14px;
  color: var(--Text1);
  font-weight: 400;
}

.resource-filter {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  width: 240px;
}

.search-input {
  width: 100%;
}

.area-select {
  width: 240px;
}

.bind-button {
  margin-left: auto;
}

/* 资源表格 */
.resource-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 分页器包装器 */
.pagination-wrapper {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
}

.pagination-total {
  color: var(--Text2);
  font-size: 14px;
}

.pagination-total .total-number {
  color: var(--ZS);
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

/* 底部按钮 */
.cancel-button {
  margin-right: 8px;
}

.confirm-button {
  margin-left: 8px;
}

/* 表格样式调整 */
.resource-table :deep(.el-table) {
  border: none;
}

.resource-table :deep(.el-table th) {
  background-color: var(--BG2);
  border-bottom: 1px solid var(--Border1);
}

.resource-table :deep(.el-table td) {
  border-bottom: 1px solid var(--Border1);
}

.resource-table :deep(.el-table__body-wrapper) {
  overflow-x: hidden;
}

/* 输入框样式 */
.form-input :deep(.el-input__inner),
.form-select :deep(.el-input__inner) {
  border: 1px solid var(--Border1);
  border-radius: 4px;
  height: 32px;
  line-height: 32px;
}

.form-input :deep(.el-input__inner):focus,
.form-select :deep(.el-input__inner):focus {
  border-color: var(--ZS);
}

.search-input :deep(.el-input__inner) {
  border: 1px solid var(--Border2);
  border-radius: 4px;
  height: 32px;
}

.area-selector :deep(.el-input__inner) {
  border: 1px solid var(--Border2);
  border-radius: 4px;
  height: 32px;
}
</style>
